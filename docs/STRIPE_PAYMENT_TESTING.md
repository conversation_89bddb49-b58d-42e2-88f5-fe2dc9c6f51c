# Stripe Payment Testing Guide

## Overview

This document describes how to test the enhanced Stripe payment functionality that includes login state checking and improved user experience.

## Features Implemented

### 1. Enhanced StripePayment Component
- **Login State Checking**: Automatically checks if user is logged in before processing payment
- **User-Friendly Error Handling**: Shows toast notifications for errors
- **Loading States**: Proper loading indicators during payment processing
- **Flexible Response Handling**: Supports both URL redirect and session ID approaches

### 2. PricingCard Component
- **Interactive Pricing Cards**: Click to reveal payment options
- **Login Prompts**: Guides users to sign in if not authenticated
- **Responsive Design**: Works on all screen sizes
- **Popular Plan Highlighting**: Visual indicators for recommended plans

### 3. PricingSection Component
- **Complete Pricing Display**: Shows multiple pricing tiers
- **Integrated Payment Flow**: Seamless transition from selection to payment

## Testing Steps

### Prerequisites
1. Ensure Stripe environment variables are set:
   ```env
   NEXT_PUBLIC_STRIPE_PUBLIC_KEY=pk_test_...
   STRIPE_PRIVATE_KEY=sk_test_...
   STRIPE_WEBHOOK_SECRET=whsec_...
   ```

2. Database should be set up with User and Order models

### Test Scenarios

#### 1. Unauthenticated User Flow
1. Navigate to `/pricing` page
2. Click on any pricing plan
3. **Expected**: User should be prompted to sign in
4. **Expected**: Toast notification appears: "Please sign in to purchase this plan"
5. **Expected**: Sign-in modal/page opens

#### 2. Authenticated User Flow
1. Sign in to the application
2. Navigate to `/pricing` page
3. Click on any pricing plan
4. **Expected**: Payment button appears
5. Click "Pay $X.XX USD" button
6. **Expected**: Redirected to Stripe Checkout
7. Complete payment with test card: `4242 4242 4242 4242`
8. **Expected**: Redirected to success URL with order details

#### 3. Error Handling
1. Test with invalid Stripe keys (temporarily)
2. **Expected**: User-friendly error messages
3. **Expected**: No application crashes

#### 4. Loading States
1. Observe loading indicators during:
   - Initial page load
   - Payment processing
   - Authentication checks

### Direct Component Testing

You can also test the StripePayment component directly:

```tsx
import StripePayment from '@/components/StripePayment';

// Basic usage
<StripePayment 
  amount={29.99} 
  currency="usd"
  productName="Pro Plan"
/>

// With custom styling
<StripePayment 
  amount={9.99}
  currency="usd"
  productName="Basic Plan"
  className="w-full py-3 px-6 bg-green-600 hover:bg-green-700 text-white rounded-lg"
>
  Custom Button Text
</StripePayment>
```

## API Endpoints

### POST /api/stripe
Creates a Stripe checkout session.

**Request Body:**
```json
{
  "price": 29.99,
  "currency": "usd",
  "email": "<EMAIL>",
  "productName": "Pro Plan",
  "successUrl": "https://yoursite.com/success",
  "cancelUrl": "https://yoursite.com/cancel"
}
```

**Response:**
```json
{
  "url": "https://checkout.stripe.com/pay/...",
  "id": "cs_test_..."
}
```

### POST /api/stripe/webhook
Handles Stripe webhook events for payment status updates.

**Supported Events:**
- `checkout.session.completed` - Payment successful
- `checkout.session.expired` - Payment session expired
- `payment_intent.payment_failed` - Payment failed

## Database Changes

The implementation uses existing Order model with these key fields:
- `stripeSessionId` - Links order to Stripe session
- `status` - Order status (pending, paid, failed, expired)
- `amount` - Payment amount in cents
- `userEmail` - Customer email
- `productName` - Product/plan name

## Security Considerations

1. **Authentication Required**: All payment requests require valid user session
2. **Server-Side Validation**: Amount and user validation on server
3. **Webhook Verification**: Stripe webhook signatures are verified
4. **Error Handling**: Sensitive error details are not exposed to client

## Troubleshooting

### Common Issues

1. **"NEXT_PUBLIC_STRIPE_PUBLIC_KEY is not set"**
   - Ensure environment variable is set and starts with `pk_`

2. **"Authentication required"**
   - User must be signed in before making payment requests

3. **"No payment session URL returned"**
   - Check Stripe API response and server logs

4. **Webhook not working**
   - Verify webhook endpoint URL in Stripe dashboard
   - Check webhook secret matches environment variable

### Debug Mode

Enable debug logging by setting:
```env
NODE_ENV=development
```

This will show detailed console logs for payment processing and webhook handling.

## Next Steps

1. Set up Stripe webhook endpoint in production
2. Configure proper success/cancel URLs
3. Add email notifications for successful payments
4. Implement subscription management if needed
5. Add analytics tracking for payment events
