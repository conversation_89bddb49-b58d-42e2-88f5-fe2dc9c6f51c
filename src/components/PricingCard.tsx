'use client';

import { useSession, signIn } from 'next-auth/react';
import { useState } from 'react';
import { toast } from 'sonner';
import StripePayment from './StripePayment';

interface PricingCardProps {
  title: string;
  price: number;
  currency?: string;
  features: string[];
  popular?: boolean;
  productName?: string;
  className?: string;
}

const PricingCard = ({
  title,
  price,
  currency = 'usd',
  features,
  popular = false,
  productName,
  className = ''
}: PricingCardProps) => {
  const { data: session, status } = useSession();
  const [showPayment, setShowPayment] = useState(false);

  const handlePriceClick = () => {
    // Check if user is logged in
    if (!session?.user?.email) {
      toast.info('Please sign in to purchase this plan');
      signIn();
      return;
    }

    // If logged in, show payment component
    setShowPayment(true);
  };

  const handleCancelPayment = () => {
    setShowPayment(false);
  };

  return (
    <div className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ${popular ? 'ring-2 ring-blue-500' : ''} ${className}`}>
      {popular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
            Most Popular
          </span>
        </div>
      )}

      <div className="text-center">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          {title}
        </h3>
        
        <div className="mb-4">
          <span className="text-4xl font-bold text-gray-900 dark:text-white">
            ${price}
          </span>
          <span className="text-gray-500 dark:text-gray-400 ml-1">
            {currency.toUpperCase()}
          </span>
        </div>

        <ul className="space-y-2 mb-6 text-left">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center text-gray-600 dark:text-gray-300">
              <svg
                className="w-4 h-4 text-green-500 mr-2 flex-shrink-0"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
              {feature}
            </li>
          ))}
        </ul>

        {!showPayment ? (
          <button
            onClick={handlePriceClick}
            className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
              popular
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white'
            }`}
            disabled={status === 'loading'}
          >
            {status === 'loading' 
              ? 'Loading...' 
              : session?.user?.email 
                ? `Get ${title}` 
                : 'Sign in to Purchase'
            }
          </button>
        ) : (
          <div className="space-y-3">
            <StripePayment
              amount={price}
              currency={currency}
              productName={productName || title}
              className="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors disabled:bg-gray-400"
            />
            <button
              onClick={handleCancelPayment}
              className="w-full py-2 px-4 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium transition-colors"
            >
              Cancel
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default PricingCard;
