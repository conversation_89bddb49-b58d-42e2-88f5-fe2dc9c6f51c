import { loadStripe } from '@stripe/stripe-js';
import { useSession, signIn } from 'next-auth/react';
import { useState } from 'react';
import { toast } from 'sonner';

if (!process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY) {
  throw new Error('NEXT_PUBLIC_STRIPE_PUBLIC_KEY is not set');
}

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY);

interface StripePaymentProps {
  amount: number;
  currency?: string;
  productName?: string;
  className?: string;
  children?: React.ReactNode;
}

export default function StripePayment({
  amount,
  currency = 'usd',
  productName,
  className,
  children
}: StripePaymentProps) {
  const { data: session, status } = useSession();
  const [isLoading, setIsLoading] = useState(false);

  // Loading state
  if (status === "loading") {
    return (
      <button
        className={className || "px-4 py-2 bg-gray-400 text-white rounded-md"}
        disabled
      >
        Loading...
      </button>
    );
  }

  const handlePayment = async () => {
    try {
      // Check if user is logged in
      if (!session?.user?.email) {
        toast.error('Please sign in to continue with payment');
        signIn();
        return;
      }

      setIsLoading(true);

      const response = await fetch('/api/stripe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          price: amount,
          currency,
          email: session.user.email,
          productName: productName || 'Purchase',
          successUrl: `${window.location.origin}/orders?session_id={CHECKOUT_SESSION_ID}&amount=${amount}`,
          cancelUrl: `${window.location.origin}/#pricing`
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Payment request failed');
      }

      const data = await response.json();

      // Handle both possible response formats
      const sessionUrl = data.url || data.id;
      if (!sessionUrl) {
        throw new Error('No payment session URL returned');
      }

      // If we have a URL, redirect directly
      if (data.url) {
        window.location.href = data.url;
        return;
      }

      // If we have a session ID, use Stripe checkout
      const stripe = await stripePromise;
      if (!stripe) {
        throw new Error('Stripe failed to load');
      }

      const { error } = await stripe.redirectToCheckout({
        sessionId: data.id,
      });

      if (error) {
        throw new Error(error.message || 'Stripe checkout failed');
      }
    } catch (error) {
      console.error('Payment error:', error);
      toast.error(error instanceof Error ? error.message : 'Payment failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // If not logged in, show login prompt
  if (!session?.user?.email) {
    return (
      <button
        onClick={() => signIn()}
        className={className || "px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"}
        disabled={isLoading}
      >
        {children || `Sign in to Pay $${amount} ${currency.toUpperCase()}`}
      </button>
    );
  }

  return (
    <button
      onClick={handlePayment}
      className={className || "px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:bg-gray-400"}
      disabled={isLoading}
    >
      {isLoading ? 'Processing...' : (children || `Pay $${amount} ${currency.toUpperCase()}`)}
    </button>
  );
}
