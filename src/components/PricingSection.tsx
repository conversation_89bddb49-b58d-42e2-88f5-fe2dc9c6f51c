'use client';

import PricingCard from './PricingCard';

const PricingSection = () => {
  const plans = [
    {
      title: 'Basic',
      price: 9.99,
      features: [
        '10 Projects',
        '5GB Storage',
        'Email Support',
        'Basic Analytics'
      ],
      productName: 'Basic Plan'
    },
    {
      title: 'Pro',
      price: 29.99,
      features: [
        'Unlimited Projects',
        '100GB Storage',
        'Priority Support',
        'Advanced Analytics',
        'Team Collaboration',
        'API Access'
      ],
      popular: true,
      productName: 'Pro Plan'
    },
    {
      title: 'Enterprise',
      price: 99.99,
      features: [
        'Everything in Pro',
        'Unlimited Storage',
        '24/7 Phone Support',
        'Custom Integrations',
        'Dedicated Account Manager',
        'SLA Guarantee'
      ],
      productName: 'Enterprise Plan'
    }
  ];

  return (
    <section className="py-12 bg-gray-50 dark:bg-gray-900" id="pricing">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Choose Your Plan
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Select the perfect plan for your needs. All plans include our core features 
            with different levels of usage and support.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {plans.map((plan, index) => (
            <PricingCard
              key={index}
              title={plan.title}
              price={plan.price}
              features={plan.features}
              popular={plan.popular}
              productName={plan.productName}
            />
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            All plans include a 30-day money-back guarantee. 
            No setup fees. Cancel anytime.
          </p>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
